// 临时构建配置：禁用 AOT 编译
// 如果 AOT 依赖解析问题无法解决，可以使用此配置

plugins {
    id 'java'
    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
    // 注释掉 GraalVM 插件以禁用 AOT
    // id 'org.graalvm.buildtools.native'
}

group = 'com.thomasvitale'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
        vendor = JvmVendorSpec.ORACLE
    }
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

repositories {
    mavenCentral()
    maven {
        url 'https://maven.aliyun.com/repository/public'
        name 'Aliyun Public Repository'
    }
    maven {
        url 'https://repo.spring.io/snapshot'
        name 'Spring Snapshot Repository'
    }
    maven {
        url 'https://repo.spring.io/milestone'
        name 'Spring Milestone Repository'
    }
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation "org.springframework.ai:spring-ai-starter-model-ollama"

    testAndDevelopmentOnly 'io.arconia:arconia-dev-services-ollama'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

dependencyManagement {
    imports {
        mavenBom "io.arconia:arconia-bom:${arconiaVersion}"
        mavenBom "org.springframework.ai:spring-ai-bom:${springAiVersion}"
    }
}

tasks.named('test') {
    useJUnitPlatform()
}

// 禁用所有 AOT 相关任务
tasks.matching { it.name.contains('Aot') || it.name.contains('AOT') }.configureEach {
    enabled = false
}
