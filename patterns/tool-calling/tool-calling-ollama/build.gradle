plugins {
    id 'java'
    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
    id 'org.graalvm.buildtools.native'
}

group = 'com.thomasvitale'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
        vendor = JvmVendorSpec.ORACLE
    }
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

repositories {
    // Maven 中央仓库
    mavenCentral()
    // 阿里云 Maven 仓库镜像（提高下载速度）
    maven {
        url 'https://maven.aliyun.com/repository/public'
        name 'Aliyun Public Repository'
    }
    // Spring 快照仓库
    maven {
        url 'https://repo.spring.io/snapshot'
        name 'Spring Snapshot Repository'
        // 添加认证和重试机制
        content {
            includeGroup 'org.springframework.ai'
            includeGroup 'org.springframework.boot'
        }
    }
    // Spring 里程碑仓库（更稳定）
    maven {
        url 'https://repo.spring.io/milestone'
        name 'Spring Milestone Repository'
    }
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation "org.springframework.ai:spring-ai-starter-model-ollama"

    testAndDevelopmentOnly 'io.arconia:arconia-dev-services-ollama'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

dependencyManagement {
    imports {
        mavenBom "io.arconia:arconia-bom:${arconiaVersion}"
        mavenBom "org.springframework.ai:spring-ai-bom:${springAiVersion}"
    }
}

tasks.named('test') {
    useJUnitPlatform()
}

// GraalVM 原生镜像配置
graalvmNative {
    binaries {
        main {
            verbose = true
            buildArgs.add('--verbose')
            buildArgs.add('--no-fallback')
        }
    }
    toolchainDetection = false
}

// 配置 AOT 处理任务
tasks.withType(org.springframework.boot.gradle.tasks.aot.ProcessAot) {
    javaLauncher = javaToolchains.launcherFor {
        languageVersion = JavaLanguageVersion.of(21)
        vendor = JvmVendorSpec.ORACLE
    }
}

// 配置 AOT 测试处理任务
tasks.withType(org.springframework.boot.gradle.tasks.aot.ProcessTestAot) {
    javaLauncher = javaToolchains.launcherFor {
        languageVersion = JavaLanguageVersion.of(21)
        vendor = JvmVendorSpec.ORACLE
    }
}
