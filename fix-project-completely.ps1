# PowerShell 脚本：全面修复 Spring AI 项目
# 解决 "Project source sets cannot be resolved" 和其他构建问题

Write-Host "开始全面修复 Spring AI 项目..." -ForegroundColor Green

# 1. 检查 Java 环境
Write-Host "`n1. 检查 Java 环境..." -ForegroundColor Yellow
$javaVersion = & java -version 2>&1
Write-Host "当前 Java 版本: $javaVersion" -ForegroundColor Cyan

if ($javaVersion -notmatch "21\.|17\.") {
    Write-Host "警告: 建议使用 Java 17 或 21" -ForegroundColor Red
}

# 2. 停止所有 Gradle 守护进程
Write-Host "`n2. 停止所有 Gradle 守护进程..." -ForegroundColor Yellow
& .\gradlew.bat --stop

# 3. 清理所有缓存
Write-Host "`n3. 清理缓存..." -ForegroundColor Yellow
Write-Host "清理项目构建缓存..." -ForegroundColor Cyan
& .\gradlew.bat clean --no-daemon

Write-Host "清理 Gradle 用户缓存..." -ForegroundColor Cyan
$gradleHome = "$env:USERPROFILE\.gradle"
if (Test-Path "$gradleHome\caches") {
    Remove-Item "$gradleHome\caches" -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "✓ Gradle 缓存已清理" -ForegroundColor Green
}

Write-Host "清理 Maven 本地仓库..." -ForegroundColor Cyan
$m2Home = "$env:USERPROFILE\.m2\repository"
if (Test-Path "$m2Home\org\springframework\ai") {
    Remove-Item "$m2Home\org\springframework\ai" -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "✓ Spring AI 依赖缓存已清理" -ForegroundColor Green
}

# 4. 修复有问题的 build.gradle 文件
Write-Host "`n4. 修复构建配置文件..." -ForegroundColor Yellow

$problematicFiles = @(
    "use-cases\text-classification\build.gradle",
    "use-cases\structured-data-extraction\build.gradle", 
    "models\chat\chat-multiple-providers\build.gradle",
    "data-ingestion\document-readers\document-readers-pdf-ollama\build.gradle"
)

foreach ($file in $problematicFiles) {
    if (Test-Path $file) {
        Write-Host "修复 $file..." -ForegroundColor Cyan
        
        # 读取文件内容
        $content = Get-Content $file -Raw
        
        # 移除 buildscript 块
        $content = $content -replace '(?s)buildscript\s*\{.*?\}\s*', ''
        
        # 移除 allprojects 块
        $content = $content -replace '(?s)allprojects\s*\{.*?\}\s*', ''
        
        # 修复 Java 工具链配置
        $content = $content -replace 'nativeImageCapable = true', 'vendor = JvmVendorSpec.ORACLE'
        
        # 添加源码兼容性配置
        if ($content -notmatch 'sourceCompatibility') {
            $content = $content -replace '(java\s*\{[^}]*)\}', '$1    sourceCompatibility = JavaVersion.VERSION_21' + "`n" + '    targetCompatibility = JavaVersion.VERSION_21' + "`n" + '}'
        }
        
        # 写回文件
        $content | Set-Content $file -Encoding UTF8
        Write-Host "✓ $file 已修复" -ForegroundColor Green
    }
}

# 5. 刷新依赖
Write-Host "`n5. 刷新依赖..." -ForegroundColor Yellow
& .\gradlew.bat --refresh-dependencies --no-daemon

# 6. 测试项目配置
Write-Host "`n6. 测试项目配置..." -ForegroundColor Yellow
Write-Host "列出所有项目..." -ForegroundColor Cyan
& .\gradlew.bat projects --no-daemon

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ 项目配置正常" -ForegroundColor Green
} else {
    Write-Host "✗ 项目配置仍有问题" -ForegroundColor Red
}

# 7. 尝试构建一个简单模块
Write-Host "`n7. 测试构建..." -ForegroundColor Yellow
Write-Host "尝试构建 chatbot 模块..." -ForegroundColor Cyan
& .\gradlew.bat :use-cases:chatbot:compileJava --no-daemon

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ 构建测试成功！" -ForegroundColor Green
    
    Write-Host "`n可以尝试运行以下命令：" -ForegroundColor Yellow
    Write-Host "# 构建所有模块" -ForegroundColor White
    Write-Host ".\gradlew.bat build --no-daemon" -ForegroundColor Cyan
    Write-Host "`n# 运行聊天机器人" -ForegroundColor White  
    Write-Host ".\gradlew.bat :use-cases:chatbot:bootRun" -ForegroundColor Cyan
    Write-Host "`n# 运行问答系统" -ForegroundColor White
    Write-Host ".\gradlew.bat :use-cases:question-answering:bootRun" -ForegroundColor Cyan
    
} else {
    Write-Host "✗ 构建仍然失败，需要进一步诊断" -ForegroundColor Red
}

Write-Host "`n修复完成！" -ForegroundColor Green
