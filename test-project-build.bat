@echo off
echo 测试项目构建状态...

echo 1. 检查 Java 版本...
java -version
echo.

echo 2. 检查 Gradle 版本...
call gradlew --version
echo.

echo 3. 停止 Gradle 守护进程...
call gradlew --stop
echo.

echo 4. 列出所有项目...
call gradlew projects --no-daemon
if %ERRORLEVEL% NEQ 0 (
    echo ✗ 项目配置有问题
    goto :error
)
echo ✓ 项目配置正常
echo.

echo 5. 测试编译一个简单模块...
call gradlew :use-cases:chatbot:compileJava --no-daemon
if %ERRORLEVEL% NEQ 0 (
    echo ✗ 编译失败
    goto :error
)
echo ✓ 编译成功
echo.

echo 6. 测试构建一个完整模块...
call gradlew :use-cases:chatbot:build --no-daemon
if %ERRORLEVEL% NEQ 0 (
    echo ✗ 构建失败
    goto :error
)
echo ✓ 构建成功
echo.

echo ========================================
echo ✓ 所有测试通过！项目可以正常构建
echo ========================================
echo.
echo 可以尝试运行以下命令：
echo.
echo # 构建所有模块
echo gradlew build --no-daemon
echo.
echo # 运行聊天机器人
echo gradlew :use-cases:chatbot:bootRun
echo.
echo # 运行问答系统  
echo gradlew :use-cases:question-answering:bootRun
echo.
goto :end

:error
echo ========================================
echo ✗ 测试失败！需要进一步修复
echo ========================================
echo.
echo 建议执行以下修复步骤：
echo 1. 运行 fix-project-completely.ps1
echo 2. 运行 fix-all-build-files-v2.ps1
echo 3. 重新运行此测试脚本

:end
pause
