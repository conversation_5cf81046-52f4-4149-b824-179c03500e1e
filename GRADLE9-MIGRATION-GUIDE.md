# Spring AI 项目 Gradle 9.0 + JDK 21 迁移指南

## 概述

本指南详细说明了如何将 Spring AI 项目从旧版本迁移到 Gradle 9.0 和 JDK 21 的兼容配置。

## 已完成的重构内容

### 1. Gradle Wrapper 升级
- ✅ 升级到 Gradle 9.0
- ✅ 更新 `gradle/wrapper/gradle-wrapper.properties`
- ✅ 使用官方分发地址

### 2. Settings.gradle 重构
- ✅ 修复重复的 `rootProject.name` 定义
- ✅ 启用 `foojay-resolver-convention` 插件
- ✅ 配置 `repositoriesMode.set(RepositoriesMode.PREFER_PROJECT)`
- ✅ 优化仓库配置顺序

### 3. BuildSrc 配置更新
- ✅ 更新插件版本以兼容 Gradle 9.0
- ✅ Spring Boot: 3.4.0
- ✅ Dependency Management: 1.1.6
- ✅ GraalVM: 0.10.3

### 4. Gradle.properties 优化
- ✅ 启用 Gradle 9.0 新特性
- ✅ 配置缓存优化
- ✅ JVM 参数调优（适配 JDK 21）
- ✅ 工具链自动检测

### 5. Build.gradle 标准化
- ✅ 移除所有 `buildscript` 块
- ✅ 移除 `allprojects` 块
- ✅ 统一 Java 工具链配置
- ✅ 标准化仓库配置
- ✅ 修复 AOT 编译配置

## 使用方法

### 快速开始

1. **运行完整迁移脚本**：
   ```powershell
   .\complete-gradle9-migration.ps1
   ```

2. **测试兼容性**：
   ```powershell
   .\test-gradle9-compatibility.ps1
   ```

3. **快速测试**：
   ```cmd
   quick-test-gradle9.bat
   ```

### 手动步骤

如果自动脚本有问题，可以手动执行：

1. **清理环境**：
   ```cmd
   gradlew --stop
   gradlew clean --no-daemon
   ```

2. **测试项目配置**：
   ```cmd
   gradlew projects --no-daemon
   ```

3. **测试编译**：
   ```cmd
   gradlew :use-cases:chatbot:compileJava --no-daemon
   ```

4. **测试构建**：
   ```cmd
   gradlew :use-cases:chatbot:build --no-daemon
   ```

5. **运行应用**：
   ```cmd
   gradlew :use-cases:chatbot:bootRun
   ```

## 关键配置变更

### Java 工具链配置
```gradle
java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
        vendor = JvmVendorSpec.ORACLE
    }
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}
```

### 仓库配置
```gradle
repositories {
    mavenCentral()
    maven {
        url 'https://maven.aliyun.com/repository/public'
        name 'Aliyun Public Repository'
    }
    maven {
        url 'https://repo.spring.io/milestone'
        name 'Spring Milestone Repository'
    }
    maven {
        url 'https://repo.spring.io/snapshot'
        name 'Spring Snapshot Repository'
    }
}
```

### AOT 配置
```gradle
tasks.withType(org.springframework.boot.gradle.tasks.aot.ProcessAot).configureEach {
    javaLauncher = javaToolchains.launcherFor {
        languageVersion = JavaLanguageVersion.of(21)
        vendor = JvmVendorSpec.ORACLE
    }
}
```

## 可运行的模块

经过重构后，以下模块应该可以正常运行：

- ✅ `use-cases:chatbot` - 聊天机器人
- ✅ `use-cases:question-answering` - 问答系统
- ✅ `use-cases:text-classification` - 文本分类
- ✅ `patterns:structured-output:structured-output-ollama` - 结构化输出

## 故障排除

### 常见问题

1. **"Project source sets cannot be resolved"**
   - 已通过移除 buildscript 块和 allprojects 块解决

2. **Java 工具链问题**
   - 已统一配置为 JDK 21 + Oracle 供应商

3. **依赖解析问题**
   - 已优化仓库配置和依赖管理

4. **AOT 编译问题**
   - 已配置正确的工具链启动器

### 如果仍有问题

1. 检查 JDK 21 是否正确安装
2. 检查 JAVA_HOME 环境变量
3. 运行 `gradlew --stop` 停止所有守护进程
4. 删除 `.gradle` 目录重新开始
5. 运行 `fix-all-gradle-files-gradle9.ps1` 批量修复

## 下一步

项目重构完成后，你可以：

1. 运行完整构建：`gradlew build --no-daemon`
2. 启动应用：`gradlew :use-cases:chatbot:bootRun`
3. 开发新功能或修复 bug
4. 使用 Gradle 9.0 的新特性进行优化

## 文件清单

重构过程中创建的文件：
- `gradle9-build-template.gradle` - 标准构建模板
- `fix-all-gradle-files-gradle9.ps1` - 批量修复脚本
- `test-gradle9-compatibility.ps1` - 兼容性测试脚本
- `complete-gradle9-migration.ps1` - 完整迁移脚本
- `quick-test-gradle9.bat` - 快速测试脚本
- `GRADLE9-MIGRATION-GUIDE.md` - 本指南
