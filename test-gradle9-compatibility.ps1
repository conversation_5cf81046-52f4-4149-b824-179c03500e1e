# PowerShell 脚本：测试 Gradle 9.0 和 JDK 21 兼容性

Write-Host "测试 Spring AI 项目与 Gradle 9.0 和 JDK 21 的兼容性..." -ForegroundColor Green

# 1. 检查环境
Write-Host "`n=== 环境检查 ===" -ForegroundColor Cyan
Write-Host "检查 Java 版本..." -ForegroundColor Yellow
try {
    $javaVersion = & java -version 2>&1
    Write-Host "Java 版本: $($javaVersion[0])" -ForegroundColor White
    
    if ($javaVersion[0] -match "21\.") {
        Write-Host "✓ Java 21 检测成功" -ForegroundColor Green
    } else {
        Write-Host "✗ 需要 Java 21，当前版本可能不兼容" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Java 检测失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n检查 JAVA_HOME..." -ForegroundColor Yellow
$javaHome = $env:JAVA_HOME
if ($javaHome) {
    Write-Host "JAVA_HOME: $javaHome" -ForegroundColor White
    if (Test-Path "$javaHome\bin\java.exe") {
        Write-Host "✓ JAVA_HOME 配置正确" -ForegroundColor Green
    } else {
        Write-Host "✗ JAVA_HOME 路径无效" -ForegroundColor Red
    }
} else {
    Write-Host "✗ JAVA_HOME 未设置" -ForegroundColor Red
}

# 2. 停止现有的 Gradle 守护进程
Write-Host "`n=== Gradle 环境准备 ===" -ForegroundColor Cyan
Write-Host "停止现有 Gradle 守护进程..." -ForegroundColor Yellow
& .\gradlew.bat --stop

# 3. 检查 Gradle 版本
Write-Host "`n检查 Gradle 版本..." -ForegroundColor Yellow
try {
    $gradleVersion = & .\gradlew.bat --version --no-daemon 2>&1
    Write-Host "Gradle 版本信息:" -ForegroundColor White
    $gradleVersion | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
    
    if ($gradleVersion -match "Gradle 9\.") {
        Write-Host "✓ Gradle 9.x 检测成功" -ForegroundColor Green
    } else {
        Write-Host "✗ 期望 Gradle 9.x，当前版本可能不兼容" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Gradle 版本检测失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 测试项目配置
Write-Host "`n=== 项目配置测试 ===" -ForegroundColor Cyan
Write-Host "测试项目列表..." -ForegroundColor Yellow
try {
    $projectsOutput = & .\gradlew.bat projects --no-daemon 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 项目配置正常" -ForegroundColor Green
        $projectCount = ($projectsOutput | Select-String "Project ':").Count
        Write-Host "  检测到 $projectCount 个子项目" -ForegroundColor Gray
    } else {
        Write-Host "✗ 项目配置失败" -ForegroundColor Red
        Write-Host "错误输出:" -ForegroundColor Red
        $projectsOutput | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
        return
    }
} catch {
    Write-Host "✗ 项目列表测试失败: $($_.Exception.Message)" -ForegroundColor Red
    return
}

# 5. 测试编译
Write-Host "`n=== 编译测试 ===" -ForegroundColor Cyan
$testModules = @(
    ":use-cases:chatbot",
    ":use-cases:question-answering",
    ":patterns:structured-output:structured-output-ollama"
)

foreach ($module in $testModules) {
    Write-Host "测试编译 $module..." -ForegroundColor Yellow
    try {
        $compileOutput = & .\gradlew.bat "$module`:compileJava" --no-daemon 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ $module 编译成功" -ForegroundColor Green
        } else {
            Write-Host "✗ $module 编译失败" -ForegroundColor Red
            Write-Host "错误输出:" -ForegroundColor Red
            $compileOutput | Select-Object -Last 10 | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
        }
    } catch {
        Write-Host "✗ $module 编译测试失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 6. 测试构建
Write-Host "`n=== 构建测试 ===" -ForegroundColor Cyan
Write-Host "测试构建 chatbot 模块..." -ForegroundColor Yellow
try {
    $buildOutput = & .\gradlew.bat ":use-cases:chatbot:build" --no-daemon 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ chatbot 模块构建成功" -ForegroundColor Green
    } else {
        Write-Host "✗ chatbot 模块构建失败" -ForegroundColor Red
        Write-Host "错误输出:" -ForegroundColor Red
        $buildOutput | Select-Object -Last 10 | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
    }
} catch {
    Write-Host "✗ 构建测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. 总结
Write-Host "`n=== 测试总结 ===" -ForegroundColor Cyan
Write-Host "Gradle 9.0 + JDK 21 兼容性测试完成" -ForegroundColor Green

Write-Host "`n如果所有测试通过，可以尝试运行：" -ForegroundColor Yellow
Write-Host "# 构建所有模块" -ForegroundColor White
Write-Host ".\gradlew.bat build --no-daemon" -ForegroundColor Cyan
Write-Host "`n# 运行聊天机器人" -ForegroundColor White
Write-Host ".\gradlew.bat :use-cases:chatbot:bootRun" -ForegroundColor Cyan
Write-Host "`n# 运行问答系统" -ForegroundColor White
Write-Host ".\gradlew.bat :use-cases:question-answering:bootRun" -ForegroundColor Cyan

Write-Host "`n如果仍有问题，请运行：" -ForegroundColor Yellow
Write-Host ".\fix-all-gradle-files-gradle9.ps1" -ForegroundColor Cyan
