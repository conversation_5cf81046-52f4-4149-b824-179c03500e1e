# PowerShell 脚本：完整的 Gradle 9.0 + JDK 21 项目迁移

Write-Host "开始完整的 Spring AI 项目迁移到 Gradle 9.0 + JDK 21..." -ForegroundColor Green

# 步骤 1: 环境检查
Write-Host "`n=== 步骤 1: 环境检查 ===" -ForegroundColor Cyan
Write-Host "检查 JDK 21 和 Gradle 9.0 环境..." -ForegroundColor Yellow

$javaVersion = & java -version 2>&1
if ($javaVersion[0] -notmatch "21\.") {
    Write-Host "✗ 错误: 需要 JDK 21，请先安装并配置 JDK 21" -ForegroundColor Red
    Write-Host "当前检测到: $($javaVersion[0])" -ForegroundColor Red
    exit 1
}
Write-Host "✓ JDK 21 环境正常" -ForegroundColor Green

# 步骤 2: 清理环境
Write-Host "`n=== 步骤 2: 清理环境 ===" -ForegroundColor Cyan
Write-Host "停止所有 Gradle 守护进程..." -ForegroundColor Yellow
& .\gradlew.bat --stop

Write-Host "清理构建缓存..." -ForegroundColor Yellow
& .\gradlew.bat clean --no-daemon

Write-Host "清理 Gradle 用户缓存..." -ForegroundColor Yellow
$gradleHome = "$env:USERPROFILE\.gradle"
if (Test-Path "$gradleHome\caches") {
    Remove-Item "$gradleHome\caches" -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "✓ Gradle 缓存已清理" -ForegroundColor Green
}

# 步骤 3: 运行批量修复脚本
Write-Host "`n=== 步骤 3: 批量修复构建文件 ===" -ForegroundColor Cyan
if (Test-Path "fix-all-gradle-files-gradle9.ps1") {
    Write-Host "运行批量修复脚本..." -ForegroundColor Yellow
    & .\fix-all-gradle-files-gradle9.ps1
} else {
    Write-Host "批量修复脚本不存在，跳过..." -ForegroundColor Yellow
}

# 步骤 4: 验证 Gradle Wrapper
Write-Host "`n=== 步骤 4: 验证 Gradle Wrapper ===" -ForegroundColor Cyan
Write-Host "检查 Gradle Wrapper 版本..." -ForegroundColor Yellow
$wrapperProps = Get-Content "gradle\wrapper\gradle-wrapper.properties" -Raw
if ($wrapperProps -match "gradle-9\.") {
    Write-Host "✓ Gradle Wrapper 已升级到 9.x" -ForegroundColor Green
} else {
    Write-Host "✗ Gradle Wrapper 版本需要手动检查" -ForegroundColor Red
}

# 步骤 5: 测试项目配置
Write-Host "`n=== 步骤 5: 测试项目配置 ===" -ForegroundColor Cyan
Write-Host "测试项目列表..." -ForegroundColor Yellow
$projectsResult = & .\gradlew.bat projects --no-daemon 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ 项目配置测试通过" -ForegroundColor Green
} else {
    Write-Host "✗ 项目配置测试失败" -ForegroundColor Red
    Write-Host "错误信息:" -ForegroundColor Red
    $projectsResult | Select-Object -Last 5 | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
    
    Write-Host "`n尝试修复常见问题..." -ForegroundColor Yellow
    # 这里可以添加自动修复逻辑
}

# 步骤 6: 测试关键模块编译
Write-Host "`n=== 步骤 6: 测试关键模块编译 ===" -ForegroundColor Cyan
$criticalModules = @(
    ":use-cases:chatbot",
    ":use-cases:question-answering",
    ":use-cases:text-classification"
)

$successCount = 0
foreach ($module in $criticalModules) {
    Write-Host "编译测试: $module" -ForegroundColor Yellow
    $compileResult = & .\gradlew.bat "$module`:compileJava" --no-daemon 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ $module 编译成功" -ForegroundColor Green
        $successCount++
    } else {
        Write-Host "✗ $module 编译失败" -ForegroundColor Red
    }
}

# 步骤 7: 生成迁移报告
Write-Host "`n=== 步骤 7: 迁移报告 ===" -ForegroundColor Cyan
$totalModules = $criticalModules.Count
$successRate = [math]::Round(($successCount / $totalModules) * 100, 2)

Write-Host "迁移完成统计:" -ForegroundColor White
Write-Host "  总测试模块: $totalModules" -ForegroundColor Gray
Write-Host "  成功编译: $successCount" -ForegroundColor Gray
Write-Host "  成功率: $successRate%" -ForegroundColor Gray

if ($successRate -ge 80) {
    Write-Host "`n✓ 迁移基本成功！" -ForegroundColor Green
    Write-Host "可以尝试运行完整构建:" -ForegroundColor Yellow
    Write-Host "  .\gradlew.bat build --no-daemon" -ForegroundColor Cyan
    
    Write-Host "`n可以尝试运行应用:" -ForegroundColor Yellow
    Write-Host "  .\gradlew.bat :use-cases:chatbot:bootRun" -ForegroundColor Cyan
    Write-Host "  .\gradlew.bat :use-cases:question-answering:bootRun" -ForegroundColor Cyan
    
} elseif ($successRate -ge 50) {
    Write-Host "`n⚠ 迁移部分成功" -ForegroundColor Yellow
    Write-Host "建议检查失败的模块并手动修复" -ForegroundColor Yellow
    
} else {
    Write-Host "`n✗ 迁移需要进一步处理" -ForegroundColor Red
    Write-Host "建议检查以下方面:" -ForegroundColor Yellow
    Write-Host "  1. JDK 21 环境配置" -ForegroundColor White
    Write-Host "  2. gradle.properties 配置" -ForegroundColor White
    Write-Host "  3. settings.gradle 配置" -ForegroundColor White
    Write-Host "  4. 各模块的 build.gradle 配置" -ForegroundColor White
}

# 步骤 8: 创建快速测试命令
Write-Host "`n=== 快速测试命令 ===" -ForegroundColor Cyan
Write-Host "创建快速测试脚本..." -ForegroundColor Yellow

$quickTestScript = @"
@echo off
echo 快速测试 Gradle 9.0 + JDK 21 项目...
echo.

echo 1. 检查 Java 版本
java -version
echo.

echo 2. 检查 Gradle 版本
call gradlew --version --no-daemon
echo.

echo 3. 列出项目
call gradlew projects --no-daemon
echo.

echo 4. 编译 chatbot 模块
call gradlew :use-cases:chatbot:compileJava --no-daemon
echo.

echo 5. 构建 chatbot 模块
call gradlew :use-cases:chatbot:build --no-daemon
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✓ 所有测试通过！
    echo 可以运行: gradlew :use-cases:chatbot:bootRun
) else (
    echo ✗ 测试失败，需要进一步检查
)

pause
"@

$quickTestScript | Set-Content "quick-test-gradle9.bat" -Encoding UTF8
Write-Host "✓ 已创建 quick-test-gradle9.bat" -ForegroundColor Green

Write-Host "`n迁移脚本执行完成！" -ForegroundColor Green
