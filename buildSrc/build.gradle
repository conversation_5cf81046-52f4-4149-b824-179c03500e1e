plugins {
    id 'groovy-gradle-plugin'
}

repositories {
    gradlePluginPortal()
    mavenCentral()
    maven {
        url 'https://maven.aliyun.com/repository/public'
        name 'Aliyun Public'
    }
}

ext {
    // 更新到与 Gradle 9.0 兼容的版本
    set("springBootVersion", '3.4.0')
    set("dependencyManagementVersion", '1.1.6')
    set("graalvmVersion", '0.10.3')
}

dependencies {
    implementation "io.spring.gradle:dependency-management-plugin:${dependencyManagementVersion}"
    implementation "org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}"
    implementation "org.graalvm.buildtools:native-gradle-plugin:${graalvmVersion}"
}
