# PowerShell 脚本：修复 AOT 依赖解析问题
# 解决 TypedResolveException: Could not resolve all dependencies for configuration 'aotCompileClasspath'

Write-Host "开始修复 AOT 依赖解析问题..." -ForegroundColor Green

# 1. 停止所有 Gradle 守护进程
Write-Host "1. 停止 Gradle 守护进程..." -ForegroundColor Yellow
& .\gradlew.bat --stop

# 2. 清理所有构建缓存
Write-Host "2. 清理构建缓存..." -ForegroundColor Yellow
& .\gradlew.bat clean --no-daemon

# 3. 清理 Gradle 用户缓存
Write-Host "3. 清理 Gradle 缓存..." -ForegroundColor Yellow
$gradleHome = "$env:USERPROFILE\.gradle"
if (Test-Path "$gradleHome\caches") {
    Remove-Item "$gradleHome\caches" -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "已清理 Gradle 缓存目录" -ForegroundColor Green
}

# 4. 清理 Maven 本地仓库中的 Spring AI 相关依赖
Write-Host "4. 清理 Maven 本地仓库..." -ForegroundColor Yellow
$m2Home = "$env:USERPROFILE\.m2\repository"
if (Test-Path "$m2Home\org\springframework\ai") {
    Remove-Item "$m2Home\org\springframework\ai" -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "已清理 Spring AI 依赖缓存" -ForegroundColor Green
}
if (Test-Path "$m2Home\io\arconia") {
    Remove-Item "$m2Home\io\arconia" -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "已清理 Arconia 依赖缓存" -ForegroundColor Green
}

# 5. 刷新依赖并重新下载
Write-Host "5. 刷新依赖..." -ForegroundColor Yellow
& .\gradlew.bat --refresh-dependencies --no-daemon

# 6. 尝试解析特定项目的依赖
Write-Host "6. 测试依赖解析..." -ForegroundColor Yellow
Write-Host "测试 tool-calling-ollama 项目依赖..." -ForegroundColor Cyan
& .\gradlew.bat :patterns:tool-calling:tool-calling-ollama:dependencies --no-daemon

# 7. 尝试编译项目（不包含 AOT）
Write-Host "7. 尝试常规编译..." -ForegroundColor Yellow
& .\gradlew.bat :patterns:tool-calling:tool-calling-ollama:compileJava --no-daemon

Write-Host "修复完成！" -ForegroundColor Green

Write-Host "`n如果问题仍然存在，请尝试以下步骤：" -ForegroundColor Yellow
Write-Host "1. 检查网络连接，确保可以访问 Maven 仓库" -ForegroundColor White
Write-Host "2. 临时禁用 AOT 编译（移除 GraalVM 插件）" -ForegroundColor White
Write-Host "3. 使用稳定版本而非快照版本" -ForegroundColor White
Write-Host "4. 检查防火墙和代理设置" -ForegroundColor White
