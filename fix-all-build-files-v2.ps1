# PowerShell 脚本：批量修复所有有问题的 build.gradle 文件
# 解决 buildscript 块和配置冲突问题

Write-Host "开始批量修复所有 build.gradle 文件..." -ForegroundColor Green

# 需要修复的文件列表
$filesToFix = @(
    "use-cases\structured-data-extraction\build.gradle",
    "models\chat\chat-multiple-providers\build.gradle", 
    "data-ingestion\document-readers\document-readers-pdf-ollama\build.gradle",
    "data-ingestion\document-readers\document-readers-json-ollama\build.gradle",
    "data-ingestion\document-readers\document-readers-markdown-ollama\build.gradle",
    "data-ingestion\document-readers\document-readers-text-ollama\build.gradle",
    "data-ingestion\document-readers\document-readers-tika-ollama\build.gradle",
    "data-ingestion\document-transformers\document-transformers-metadata-ollama\build.gradle",
    "data-ingestion\document-transformers\document-transformers-splitters-ollama\build.gradle",
    "models\embedding\embedding-mistral-ai\build.gradle",
    "models\embedding\embedding-ollama\build.gradle",
    "models\embedding\embedding-openai\build.gradle",
    "models\embedding\embedding-transformers\build.gradle"
)

foreach ($file in $filesToFix) {
    if (Test-Path $file) {
        Write-Host "`n修复 $file..." -ForegroundColor Yellow
        
        try {
            # 读取文件内容
            $content = Get-Content $file -Raw -Encoding UTF8
            
            # 备份原文件
            $backupFile = $file + ".backup"
            Copy-Item $file $backupFile -Force
            
            # 1. 移除 buildscript 块
            $content = $content -replace '(?s)buildscript\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}\s*\n?', ''
            
            # 2. 移除 allprojects 块
            $content = $content -replace '(?s)allprojects\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}\s*\n?', ''
            
            # 3. 修复 Java 工具链配置
            $content = $content -replace 'nativeImageCapable = true', 'vendor = JvmVendorSpec.ORACLE'
            
            # 4. 确保有正确的仓库配置
            if ($content -notmatch 'maven.*aliyun') {
                $content = $content -replace '(repositories\s*\{\s*)', '$1' + "`n" + '    maven {' + "`n" + '        url ''https://maven.aliyun.com/repository/public''' + "`n" + '        name ''Aliyun Public Repository''' + "`n" + '    }' + "`n"
            }
            
            # 5. 添加源码兼容性配置（如果没有）
            if ($content -notmatch 'sourceCompatibility' -and $content -match 'java\s*\{') {
                $content = $content -replace '(java\s*\{[^}]*)(toolchain\s*\{[^}]*\})', '$1$2' + "`n" + '    sourceCompatibility = JavaVersion.VERSION_21' + "`n" + '    targetCompatibility = JavaVersion.VERSION_21'
            }
            
            # 6. 清理多余的空行
            $content = $content -replace '\n\s*\n\s*\n', "`n`n"
            
            # 写回文件
            $content | Set-Content $file -Encoding UTF8 -NoNewline
            
            Write-Host "✓ $file 修复完成" -ForegroundColor Green
            
        } catch {
            Write-Host "✗ 修复 $file 时出错: $($_.Exception.Message)" -ForegroundColor Red
            
            # 恢复备份
            if (Test-Path $backupFile) {
                Copy-Item $backupFile $file -Force
                Remove-Item $backupFile -Force
            }
        }
    } else {
        Write-Host "文件不存在: $file" -ForegroundColor Gray
    }
}

Write-Host "`n批量修复完成！" -ForegroundColor Green
Write-Host "现在可以尝试运行:" -ForegroundColor Yellow
Write-Host ".\gradlew.bat projects --no-daemon" -ForegroundColor Cyan
