# PowerShell 脚本：测试 Maven 仓库连接
# 诊断依赖解析问题是否由网络连接引起

Write-Host "测试 Maven 仓库连接..." -ForegroundColor Green

# 测试的仓库列表
$repositories = @(
    @{Name="Maven Central"; Url="https://repo1.maven.org/maven2/"},
    @{Name="Aliyun Public"; Url="https://maven.aliyun.com/repository/public/"},
    @{Name="Spring Snapshot"; Url="https://repo.spring.io/snapshot/"},
    @{Name="Spring Milestone"; Url="https://repo.spring.io/milestone/"}
)

foreach ($repo in $repositories) {
    Write-Host "`n测试 $($repo.Name)..." -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri $repo.Url -Method Head -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✓ $($repo.Name) 连接正常" -ForegroundColor Green
        } else {
            Write-Host "✗ $($repo.Name) 返回状态码: $($response.StatusCode)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "✗ $($repo.Name) 连接失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试特定的 Spring AI 依赖
Write-Host "`n测试 Spring AI 依赖可用性..." -ForegroundColor Yellow
$springAiUrl = "https://repo.spring.io/snapshot/org/springframework/ai/spring-ai-bom/"
try {
    $response = Invoke-WebRequest -Uri $springAiUrl -Method Head -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Spring AI BOM 可访问" -ForegroundColor Green
    } else {
        Write-Host "✗ Spring AI BOM 访问异常" -ForegroundColor Red
    }
}
catch {
    Write-Host "✗ Spring AI BOM 无法访问: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试 Arconia 依赖
Write-Host "`n测试 Arconia 依赖可用性..." -ForegroundColor Yellow
$arconiaUrl = "https://repo1.maven.org/maven2/io/arconia/arconia-bom/"
try {
    $response = Invoke-WebRequest -Uri $arconiaUrl -Method Head -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Arconia BOM 可访问" -ForegroundColor Green
    } else {
        Write-Host "✗ Arconia BOM 访问异常" -ForegroundColor Red
    }
}
catch {
    Write-Host "✗ Arconia BOM 无法访问: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n网络诊断完成！" -ForegroundColor Green
