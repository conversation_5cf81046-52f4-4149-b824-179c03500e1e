// 多提供商聊天模块 - 兼容 Gradle 9.0 和 JDK 21

plugins {
    id 'java'
    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
    id 'org.graalvm.buildtools.native'
}

group = 'com.thomasvitale'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
        vendor = JvmVendorSpec.ORACLE
    }
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

repositories {
    mavenCentral()
    maven {
        url 'https://maven.aliyun.com/repository/public'
        name 'Aliyun Public Repository'
    }
    maven {
        url 'https://repo.spring.io/milestone'
        name 'Spring Milestone Repository'
    }
    maven {
        url 'https://repo.spring.io/snapshot'
        name 'Spring Snapshot Repository'
    }
}

dependencies {
    implementation platform("org.springframework.ai:spring-ai-bom:${springAiVersion}")

    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.ai:spring-ai-starter-model-mistral-ai'
    implementation 'org.springframework.ai:spring-ai-starter-model-openai'

    developmentOnly 'org.springframework.boot:spring-boot-devtools'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

tasks.named('test') {
    useJUnitPlatform()
}
