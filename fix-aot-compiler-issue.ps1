# PowerShell 脚本：修复 AOT 编译器问题
# 解决 PropertyQueryException: Failed to calculate the value of task 'compileAotJava' property 'javaCompiler'

Write-Host "开始修复 AOT 编译器问题..." -ForegroundColor Green

# 1. 检查 Java 环境
Write-Host "1. 检查 Java 环境..." -ForegroundColor Yellow
$javaHome = $env:JAVA_HOME
if ($javaHome) {
    Write-Host "JAVA_HOME: $javaHome" -ForegroundColor Cyan
    & "$javaHome\bin\java" -version
} else {
    Write-Host "警告: JAVA_HOME 未设置" -ForegroundColor Red
}

# 2. 停止所有 Gradle 守护进程
Write-Host "2. 停止 Gradle 守护进程..." -ForegroundColor Yellow
& .\gradlew.bat --stop

# 3. 清理构建缓存
Write-Host "3. 清理构建缓存..." -ForegroundColor Yellow
& .\gradlew.bat clean --no-daemon

# 4. 清理 Gradle 缓存目录
Write-Host "4. 清理 Gradle 缓存..." -ForegroundColor Yellow
$gradleHome = "$env:USERPROFILE\.gradle"
if (Test-Path "$gradleHome\caches") {
    Remove-Item "$gradleHome\caches" -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "已清理 Gradle 缓存目录" -ForegroundColor Green
}

# 5. 刷新依赖
Write-Host "5. 刷新依赖..." -ForegroundColor Yellow
& .\gradlew.bat --refresh-dependencies --no-daemon

# 6. 尝试编译特定模块
Write-Host "6. 尝试编译 rag-advanced 模块..." -ForegroundColor Yellow
& .\gradlew.bat :rag:rag-sequential:rag-advanced:compileJava --no-daemon --info

Write-Host "修复完成！" -ForegroundColor Green
Write-Host "如果问题仍然存在，请检查以下几点：" -ForegroundColor Yellow
Write-Host "1. 确保 Java 21 正确安装并配置" -ForegroundColor White
Write-Host "2. 检查 JAVA_HOME 环境变量" -ForegroundColor White
Write-Host "3. 确保没有多个 Java 版本冲突" -ForegroundColor White
Write-Host "4. 考虑禁用 AOT 编译（如果不需要原生镜像）" -ForegroundColor White
