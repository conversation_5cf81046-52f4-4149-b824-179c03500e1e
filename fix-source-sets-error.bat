@echo off
echo 修复"Project source sets cannot be resolved"错误...

echo 1. 检查Java版本...
java -version
echo JAVA_HOME=%JAVA_HOME%

echo.
echo 2. 停止所有Gradle守护进程...
gradlew --stop

echo.
echo 3. 清理项目缓存...
gradlew clean --no-daemon

echo.
echo 4. 删除Gradle缓存目录...
if exist ".gradle" (
    rmdir /s /q ".gradle"
    echo Gradle缓存已删除
)

echo.
echo 5. 删除IntelliJ IDEA缓存...
if exist ".idea" (
    if exist ".idea\caches" (
        rmdir /s /q ".idea\caches"
        echo IntelliJ缓存已删除
    )
)

echo.
echo 6. 重新构建项目...
gradlew build --no-daemon --refresh-dependencies

if %ERRORLEVEL% EQU 0 (
    echo ✓ 修复成功！
    echo.
    echo 请在IntelliJ IDEA中执行以下操作：
    echo 1. File ^> Invalidate Caches and Restart
    echo 2. 重启后：File ^> Reload Gradle Project
) else (
    echo ✗ 构建仍然失败
    echo 请检查Java版本是否为21
)

pause
