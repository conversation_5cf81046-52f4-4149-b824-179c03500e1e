# PowerShell 脚本：批量修复所有 build.gradle 文件以兼容 Gradle 9.0 和 JDK 21

Write-Host "开始批量修复所有 build.gradle 文件以兼容 Gradle 9.0..." -ForegroundColor Green

# 需要修复的文件列表
$filesToFix = @(
    "use-cases\structured-data-extraction\build.gradle",
    "models\chat\chat-multiple-providers\build.gradle",
    "data-ingestion\document-readers\document-readers-json-ollama\build.gradle",
    "data-ingestion\document-readers\document-readers-markdown-ollama\build.gradle",
    "data-ingestion\document-readers\document-readers-text-ollama\build.gradle",
    "data-ingestion\document-readers\document-readers-tika-ollama\build.gradle",
    "data-ingestion\document-transformers\document-transformers-metadata-ollama\build.gradle",
    "data-ingestion\document-transformers\document-transformers-splitters-ollama\build.gradle",
    "models\embedding\embedding-mistral-ai\build.gradle",
    "models\embedding\embedding-ollama\build.gradle",
    "models\embedding\embedding-openai\build.gradle",
    "models\embedding\embedding-transformers\build.gradle",
    "models\chat\chat-mistral-ai\build.gradle",
    "models\chat\chat-ollama\build.gradle",
    "models\chat\chat-openai\build.gradle",
    "models\image\image-openai\build.gradle",
    "models\audio\speech-to-text-openai\build.gradle",
    "models\audio\text-to-speech-openai\build.gradle"
)

# 标准化的构建文件内容模板
$standardBuildContent = @"
// 标准化的 Spring AI 模块构建文件 - 兼容 Gradle 9.0 和 JDK 21

plugins {
    id 'java'
    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
    id 'org.graalvm.buildtools.native'
}

group = 'com.thomasvitale'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
        vendor = JvmVendorSpec.ORACLE
    }
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

repositories {
    mavenCentral()
    maven {
        url 'https://maven.aliyun.com/repository/public'
        name 'Aliyun Public Repository'
    }
    maven {
        url 'https://repo.spring.io/milestone'
        name 'Spring Milestone Repository'
    }
    maven {
        url 'https://repo.spring.io/snapshot'
        name 'Spring Snapshot Repository'
    }
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.ai:spring-ai-starter-model-ollama'

    testAndDevelopmentOnly 'io.arconia:arconia-dev-services-ollama'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework:spring-webflux'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

dependencyManagement {
    imports {
        mavenBom "io.arconia:arconia-bom:`${arconiaVersion}"
        mavenBom "org.springframework.ai:spring-ai-bom:`${springAiVersion}"
    }
}

tasks.named('test') {
    useJUnitPlatform()
}

// GraalVM 原生镜像配置 - 兼容 Gradle 9.0
graalvmNative {
    binaries {
        main {
            verbose = true
            buildArgs.add('--verbose')
            buildArgs.add('--no-fallback')
        }
    }
    toolchainDetection = false
}

// 配置 AOT 处理任务 - 兼容 Gradle 9.0
tasks.withType(org.springframework.boot.gradle.tasks.aot.ProcessAot).configureEach {
    javaLauncher = javaToolchains.launcherFor {
        languageVersion = JavaLanguageVersion.of(21)
        vendor = JvmVendorSpec.ORACLE
    }
}
"@

foreach ($file in $filesToFix) {
    if (Test-Path $file) {
        Write-Host "`n修复 $file..." -ForegroundColor Yellow
        
        try {
            # 备份原文件
            $backupFile = $file + ".backup-gradle9"
            Copy-Item $file $backupFile -Force
            
            # 读取现有文件以保留特定的依赖配置
            $existingContent = Get-Content $file -Raw -Encoding UTF8
            
            # 检查是否有特殊依赖需要保留
            $specialDeps = @()
            if ($existingContent -match 'spring-ai-pdf-document-reader') {
                $specialDeps += "    implementation 'org.springframework.ai:spring-ai-pdf-document-reader'"
            }
            if ($existingContent -match 'spring-ai-markdown-document-reader') {
                $specialDeps += "    implementation 'org.springframework.ai:spring-ai-markdown-document-reader'"
            }
            if ($existingContent -match 'spring-ai-starter-vector-store') {
                $specialDeps += "    implementation 'org.springframework.ai:spring-ai-starter-vector-store'"
            }
            if ($existingContent -match 'spring-ai-rag') {
                $specialDeps += "    implementation 'org.springframework.ai:spring-ai-rag'"
            }
            
            # 如果有特殊依赖，添加到标准模板中
            if ($specialDeps.Count -gt 0) {
                $customContent = $standardBuildContent -replace 
                    "implementation 'org.springframework.ai:spring-ai-starter-model-ollama'", 
                    ("implementation 'org.springframework.ai:spring-ai-starter-model-ollama'" + "`n" + ($specialDeps -join "`n"))
            } else {
                $customContent = $standardBuildContent
            }
            
            # 写入新的标准化内容
            $customContent | Set-Content $file -Encoding UTF8
            
            Write-Host "✓ $file 修复完成" -ForegroundColor Green
            
        } catch {
            Write-Host "✗ 修复 $file 时出错: $($_.Exception.Message)" -ForegroundColor Red
            
            # 恢复备份
            if (Test-Path $backupFile) {
                Copy-Item $backupFile $file -Force
                Remove-Item $backupFile -Force
            }
        }
    } else {
        Write-Host "文件不存在: $file" -ForegroundColor Gray
    }
}

Write-Host "`n批量修复完成！" -ForegroundColor Green
Write-Host "现在可以尝试运行:" -ForegroundColor Yellow
Write-Host ".\gradlew.bat --stop" -ForegroundColor Cyan
Write-Host ".\gradlew.bat projects --no-daemon" -ForegroundColor Cyan
