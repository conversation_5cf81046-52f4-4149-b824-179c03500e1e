# Gradle 命令使用指南

## 常见错误解决

### "Task 'wrapper' not found in project" 错误

**错误原因**: `wrapper` 任务只存在于根项目中，不能在子项目中执行。

**正确用法**:
```bash
# 在根目录执行 wrapper 任务
./gradlew wrapper

# 或者从任何位置指定根项目
./gradlew :wrapper
```

## 项目构建命令

### 构建所有项目
```bash
./gradlew build
```

### 构建特定项目
```bash
# 构建 text-classification 项目
./gradlew :use-cases:text-classification:build

# 构建 rag-advanced 项目
./gradlew :rag:rag-sequential:rag-advanced:build
```

### 运行特定项目
```bash
# 运行 text-classification 项目
./gradlew :use-cases:text-classification:bootRun

# 运行 rag-advanced 项目
./gradlew :rag:rag-sequential:rag-advanced:bootRun
```

### 清理项目
```bash
# 清理所有项目
./gradlew clean

# 清理特定项目
./gradlew :use-cases:text-classification:clean
```

### 运行测试
```bash
# 运行所有测试
./gradlew test

# 运行特定项目的测试
./gradlew :use-cases:text-classification:test
```

## 查看可用任务

### 查看根项目任务
```bash
./gradlew tasks
```

### 查看特定项目任务
```bash
./gradlew :use-cases:text-classification:tasks
```

### 查看所有项目的所有任务
```bash
./gradlew tasks --all
```

## 依赖管理

### 查看依赖
```bash
# 查看特定项目的依赖
./gradlew :use-cases:text-classification:dependencies
```

### 刷新依赖
```bash
./gradlew --refresh-dependencies
```

## 故障排除

### 清理并重建
```bash
./gradlew clean build --refresh-dependencies
```

### 停止 Gradle 守护进程
```bash
./gradlew --stop
```

### 使用详细输出调试
```bash
./gradlew :use-cases:text-classification:build --info
./gradlew :use-cases:text-classification:build --debug
```

## 项目结构

当前项目包含以下主要模块：
- `data-ingestion/*` - 数据摄取模块
- `models/*` - AI 模型模块
- `patterns/*` - 设计模式示例
- `rag/*` - RAG (检索增强生成) 模块
- `use-cases/*` - 用例示例模块
- `observability/*` - 可观测性模块
- `mcp/*` - MCP 客户端模块
