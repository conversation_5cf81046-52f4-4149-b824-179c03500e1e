@echo off
echo 构建 Text Classification 项目...

echo 1. 清理项目...
call gradlew :use-cases:text-classification:clean

echo 2. 编译项目...
call gradlew :use-cases:text-classification:compileJava

echo 3. 运行测试...
call gradlew :use-cases:text-classification:test

echo 4. 构建项目...
call gradlew :use-cases:text-classification:build

echo 构建完成！

echo.
echo 可用的命令：
echo - 运行项目: gradlew :use-cases:text-classification:bootRun
echo - 查看任务: gradlew :use-cases:text-classification:tasks
echo - 查看依赖: gradlew :use-cases:text-classification:dependencies

pause
